package ma.almobadara.backend.config;

import ma.almobadara.backend.enumeration.RoleCode;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.repository.administration.RolePrivilegeRepository;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class AccessChecker {

    private final RolePrivilegeRepository rolePrivilegeRepository;

    public AccessChecker(RolePrivilegeRepository rolePrivilegeRepository) {
        this.rolePrivilegeRepository = rolePrivilegeRepository;
    }

    public boolean hasAccess(String featureCode, String privilegeCode) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !(authentication.getPrincipal() instanceof CacheAdUser connectedUser)) {
            return false;
        }
        Long profileId = connectedUser.getRole().getId();
        if(connectedUser.getRole().getCode().equals(RoleCode.ADMIN.getCode())){
            return true;
        }
        return rolePrivilegeRepository.existsByRoleIdAndFeatureCodeAndPrivilegeCode(
                profileId, featureCode, privilegeCode
        );
    }

    public boolean hasAnyAccess(String[] permissions) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !(authentication.getPrincipal() instanceof CacheAdUser connectedUser)) {
            return false;
        }


        Long profileId = connectedUser.getRole().getId();


        for (String permission : permissions) {
            String[] parts = permission.split(":");
            if (parts.length != 2) {
                throw new IllegalArgumentException("Invalid permission format. Expected 'featureCode:privilegeCode'.");
            }

            String featureCode = parts[0];
            String privilegeCode = parts[1];


            if (rolePrivilegeRepository.existsByRoleIdAndFeatureCodeAndPrivilegeCode(profileId, featureCode, privilegeCode)) {
                return true;
            }
        }

        return false; 
    }

}
