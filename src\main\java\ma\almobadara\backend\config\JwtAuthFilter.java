package ma.almobadara.backend.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

@Component
@RequiredArgsConstructor
public class JwtAuthFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;

    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            Fi<PERSON><PERSON>hain filter<PERSON>hain
    ) throws ServletException, IOException {
        // Get the path
        String path = request.getRequestURI();

        // Only apply this filter to /mobile/ paths that are not login, refresh, or reset-password endpoints
        // Skip authentication for permitAll endpoints defined in SecurityConfig
        if (path.startsWith("/mobile/") && !path.matches(".*/view-report-with-donor.*") && !path.matches(".*/login.*") && !path.matches(".*/refresh.*") && !path.matches(".*/reset-password.*") && !path.matches(".*/assistant.*")) {
            String token = request.getHeader("Authorisation");

            // Check if token starts with "Bearer " and remove it
            if (StringUtils.hasText(token) && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            if (StringUtils.hasText(token) && jwtUtils.isValidAccessToken(token)) {
                String email = jwtUtils.extractEmail(token);
                UsernamePasswordAuthenticationToken authToken =
                        new UsernamePasswordAuthenticationToken(email, null, Collections.emptyList());
                SecurityContextHolder.getContext().setAuthentication(authToken);
            } else {
                // If no valid token is provided, return 401 Unauthorized
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Unauthorized: Valid Authorization header is required");
                return; // Stop the filter chain
            }
        }

        filterChain.doFilter(request, response);
    }
}
