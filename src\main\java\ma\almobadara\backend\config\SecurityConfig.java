package ma.almobadara.backend.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.oauth2.OAuth2ResourceServerConfiguration;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.service.administration.CacheAdUserService;
import ma.almobadara.backend.service.authRefrentiel.AuthenticationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtValidators;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
@Slf4j
@RequiredArgsConstructor
public class SecurityConfig {

    private final OAuth2ResourceServerConfiguration oAuth2ResourceServerConfiguration;
    @Value("${almobadra.config.allowed-origin:*}")
    private List<String> allowedOriginPatterns;
    private final CacheAdUserRepository cacheAdUserRepository;
    private final JwtAuthFilter jwtAuthFilter;

    @Bean
    public JwtDecoder jwtDecoder() {
        log.debug("Set OAuth2 JWT validation audience to '{}'", oAuth2ResourceServerConfiguration.getClientId());
        OAuth2TokenValidator<Jwt> audienceValidator = new AudienceValidator(oAuth2ResourceServerConfiguration.getClientId(), cacheAdUserRepository);

        log.debug("Set OAuth2 Issuer-uri to '{}'", oAuth2ResourceServerConfiguration.getIssuerUri());
        OAuth2TokenValidator<Jwt> withIssuer = JwtValidators.createDefaultWithIssuer(oAuth2ResourceServerConfiguration.getIssuerUri());
        OAuth2TokenValidator<Jwt> withAudience = new DelegatingOAuth2TokenValidator<>(withIssuer, audienceValidator);

        log.debug("Set OAuth2 Jwk-set-uri to '{}'", oAuth2ResourceServerConfiguration.getJwkSetUri());
        NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder.withJwkSetUri(oAuth2ResourceServerConfiguration.getJwkSetUri()).build();
        jwtDecoder.setJwtValidator(withAudience);

        return jwtDecoder;
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers("/api-docs", "/swagger-ui/**", "/actuator/health", "/batch/**").permitAll()
                        .requestMatchers("/mobile/*/login/**","/mobile/*/view-report-with-donor/**", "/mobile/*/refresh/**", "/mobile/*/reset-password/**").permitAll()
                        .anyRequest().authenticated()
                )
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .oauth2ResourceServer(oauth2 -> oauth2
                        .jwt(jwtConfigurer -> jwtConfigurer.jwtAuthenticationConverter(jwt -> (AbstractAuthenticationToken) new CustomJwtAuthenticationConverter().convert(jwt)))
                )
                .addFilterBefore(jwtAuthFilter, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        var corsConfiguration = new CorsConfiguration();
        corsConfiguration.setAllowedHeaders(List.of("*"));
        corsConfiguration.setAllowedMethods(List.of("*"));
        corsConfiguration.setAllowedOriginPatterns(allowedOriginPatterns);

        var source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);
        return source;
    }

    static class AudienceValidator implements OAuth2TokenValidator<Jwt> {
        private final String audience;
        private final CacheAdUserRepository cacheAdUserRepository;

        AudienceValidator(String audience, CacheAdUserRepository cacheAdUserRepository) {
            this.audience = audience;
            this.cacheAdUserRepository = cacheAdUserRepository;
        }

        @Override
        public OAuth2TokenValidatorResult validate(Jwt jwt) {
            // Check if the JWT has the expected audience
            if (jwt.getAudience().contains(audience)) {
                // Extract the Azure ID (oid) from the JWT claims
                String azureId = jwt.getClaimAsString("oid");

                if (azureId == null || azureId.isEmpty()) {
                    OAuth2Error error = new OAuth2Error("invalid_token", "Azure ID (oid) is missing", null);
                    return OAuth2TokenValidatorResult.failure(error);
                }
                // Perform the user check using the Azure ID (oid)
                CacheAdUser connectedUser = cacheAdUserRepository.findByAzureDirectoryIdAndIsDeletedIsFalse(azureId);
                if (connectedUser == null) {
                    OAuth2Error error = new OAuth2Error("invalid_token", "User not found for Azure ID: " + azureId, null);
                    return OAuth2TokenValidatorResult.failure(error);
                }

                // Step 2: Create an Authentication object
                Authentication authentication = new UsernamePasswordAuthenticationToken(connectedUser, null, null);

                 //Step 3: Set the Authentication object into the SecurityContext
                SecurityContextHolder.getContext().setAuthentication(authentication);
                return OAuth2TokenValidatorResult.success();
            } else {
                // Invalid audience error
                OAuth2Error error = new OAuth2Error("invalid_token", "Invalid audience", null);
                return OAuth2TokenValidatorResult.failure(error);
            }
        }
    }
}
