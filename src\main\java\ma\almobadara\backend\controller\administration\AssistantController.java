package ma.almobadara.backend.controller.administration;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserAlreadyExistsException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.service.administration.AssistantService;
import ma.almobadara.backend.service.notification.NotificationService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/assistants")
@Slf4j
@AllArgsConstructor
public class AssistantController {

    private final AssistantService assistantService;
    private final NotificationService notificationService;

    @GetMapping
    public ResponseEntity<Page<AssistantDTO>> getAllAssistants(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size) {
        log.info("Start resource get all assistants with page {} and size {}", page, size);
        Page<AssistantDTO> assistants = assistantService.getAllAssistants(page, size);
        log.info("End resource get all assistants with page {} and size {}", page, size);
        return ResponseEntity.ok(assistants);
    }

    @GetMapping("/getAllAssistants")
    public ResponseEntity<List<AssistantDTO>> getAssistants( ) {
        List<AssistantDTO> assistants = assistantService.getAssistants();
         return ResponseEntity.ok(assistants);
    }

//    @GetMapping("/{id}")
//    public ResponseEntity<AssistantDTO> getAssistantById(@PathVariable Long id) {
//        Optional<AssistantDTO> assistant = assistantService.getAssistantById(id);
//        return assistant.map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }


    @GetMapping("/{id}")
    public ResponseEntity<AssistantDTO> getAssistantById(@PathVariable Long id) {

        AssistantDTO assistantDTO = null;
        HttpStatus status;
        try {
            assistantDTO = assistantService.getAssistantById(id);
            status = HttpStatus.OK;
            log.info("End resource getAssistantByID : {} OK", id);
        } catch (Exception e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getAssistantByID : {}. KO: {}", id, e.getMessage());
        }

        return new ResponseEntity<>(assistantDTO, status);
    }

  /*  @PostMapping
    public ResponseEntity<AssistantDTO> createAssistant(@RequestBody AssistantDTO assistantDTO) throws TechnicalException {
        log.info("Creating a new assistant: {}", assistantDTO);
        AssistantDTO createdAssistant = assistantService.createAssistant(assistantDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdAssistant);
    }

   */

    @PostMapping(consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<Object> createAssistant(@Valid @ModelAttribute AssistantDTO assistantDTO) {
        try {
            assistantService.createAssistant(assistantDTO);
            return ResponseEntity.ok().body(Map.of("message", "User loaded and saved successfully with id " + assistantDTO.getId()));
        } catch (UserNotFoundException ex) {
            log.error("User not found with email: {}", assistantDTO.getEmail());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "User not found", "message", "User not found with email: " + assistantDTO.getEmail()));
        } catch (UserAlreadyExistsException ex) {
            log.error("User already exists with email: {}", assistantDTO.getEmail());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(Map.of("error", "User already exists", "message", "Assistant already exists with email: " + assistantDTO.getEmail()));
        } catch (TechnicalException ex) {
            log.error("Technical error while creating assistant: {}", ex.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Technical error", "message", ex.getMessage()));
        } catch (RuntimeException ex) {
            log.error("Unexpected error while creating assistant: {}", ex.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error", "message", ex.getMessage()));
        }
    }

    @PutMapping(value = "/{id}", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<Object> updateAssistant(@PathVariable Long id, @Valid @ModelAttribute AssistantDTO assistantDTO) {
        log.debug("Start resource update assistant with id {}", id);
        try {
            assistantService.updateAssistant(id, assistantDTO);
            log.debug("End resource update assistant with id {}", id);
            return ResponseEntity.ok().body(Map.of("message", "Assistant updated successfully with id " + id));
        } catch (EntityNotFoundException ex) {
            log.error("Assistant not found with id: {}", id, ex);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "Assistant not found", "message", "Assistant not found with id: " + id));
        } catch (TechnicalException ex) {
            log.error("Error updating assistant with id {}: {}", id, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Technical error", "message", "Error updating assistant: " + ex.getMessage()));
        } catch (RuntimeException ex) {
            log.error("Unexpected error while updating assistant with id {}: {}", id, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error", "message", "An unexpected error occurred: " + ex.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Object> deleteAssistant(@PathVariable Long id) throws TechnicalException {
        log.info("Start resource deleteAssistant with id: {}", id);
        assistantService.deleteAssistant(id);
        log.info("End resource deleteAssistant with id: {}", id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();

    }

    // get dispomobile zones
    @GetMapping("/disponiblesZones")
    public ResponseEntity<List<ZoneDTO>> getDisponiblesZones() {
        List<ZoneDTO> assistants = assistantService.getDisponibleZones();
        return ResponseEntity.ok(assistants);
    }


    @GetMapping("/by-email")
    public ResponseEntity<Object> getAssistantByEmail(@RequestParam String email) {
        log.debug("Start resource get assistant by email {}", email);

        try {
            AssistantDTO assistantDTO = assistantService.getAssistantByEmail(email);
            log.debug("End resource get assistant by email {}", email);
            return ResponseEntity.ok(assistantDTO);
        } catch (EntityNotFoundException ex) {
            log.error("Assistant not found with email: {}", email, ex);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "Assistant not found", "message", "Assistant not found with email: " + email));
        } catch (TechnicalException ex) {
            log.error("Error getting assistant with email {}: {}", email, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Technical error", "message", ex.getMessage()));
        } catch (Exception ex) {
            log.error("Unexpected error while getting assistant with email {}: {}", email, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error", "message", "An unexpected error occurred"));
        }
    }

    /**
     * Updates the device token for an assistant
     * @param id The ID of the assistant
     * @param payload Map containing the device_token
     * @return ResponseEntity with the updated assistant or error message
     */
    @PutMapping("/{id}/device-token")
    public ResponseEntity<Object> updateDeviceToken(
            @PathVariable Long id,
            @RequestBody Map<String, String> payload) {
        log.debug("Start resource update device token for assistant with id {}", id);

        String deviceToken = payload.get("device_token");
        if (deviceToken == null || deviceToken.isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(Map.of("error", "Invalid request", "message", "Device token is required"));
        }

        try {
            AssistantDTO assistantDTO = assistantService.updateDeviceToken(id, deviceToken);
            log.debug("End resource update device token for assistant with id {}", id);
            return ResponseEntity.ok()
                    .body(Map.of("message", "Device token updated successfully", "assistant", assistantDTO));
        } catch (EntityNotFoundException ex) {
            log.error("Assistant not found with id: {}", id, ex);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "Assistant not found", "message", "Assistant not found with id: " + id));
        } catch (TechnicalException ex) {
            log.error("Error updating device token for assistant with id {}: {}", id, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Technical error", "message", ex.getMessage()));
        } catch (Exception ex) {
            log.error("Unexpected error while updating device token for assistant with id {}: {}", id, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error", "message", "An unexpected error occurred"));
        }
    }

    /**
     * Sends a push notification to an assistant
     * @param id The ID of the assistant
     * @param payload Map containing the title and body of the notification
     * @return ResponseEntity with success or error message
     */
    @PostMapping("/{id}/send-notification")
    public ResponseEntity<Object> sendNotification(
            @PathVariable Long id,
            @RequestBody Map<String, String> payload) {
        log.debug("Start resource send notification to assistant with id {}", id);

        String title = payload.get("title");
        String body = payload.get("body");

        if (title == null || title.isEmpty() || body == null || body.isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(Map.of("error", "Invalid request", "message", "Title and body are required"));
        }

        try {
            boolean success = notificationService.sendNotification(id, title, body);
            log.debug("End resource send notification to assistant with id {}", id);

            if (success) {
                return ResponseEntity.ok()
                        .body(Map.of("message", "Notification sent successfully"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "Notification failed", "message", "Failed to send notification"));
            }
        } catch (EntityNotFoundException ex) {
            log.error("Assistant not found with id: {}", id, ex);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "Assistant not found", "message", "Assistant not found with id: " + id));
        } catch (TechnicalException ex) {
            log.error("Error sending notification to assistant with id {}: {}", id, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Technical error", "message", ex.getMessage()));
        } catch (Exception ex) {
            log.error("Unexpected error while sending notification to assistant with id {}: {}", id, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error", "message", "An unexpected error occurred"));
        }
    }

    /**
     * Sends a push notification to an assistant by email
     * @param email The email of the assistant
     * @param payload Map containing the title and body of the notification
     * @return ResponseEntity with success or error message
     */
    @PostMapping("/send-notification-by-email")
    public ResponseEntity<Object> sendNotificationByEmail(
            @RequestParam String email,
            @RequestBody Map<String, String> payload) {
        log.debug("Start resource send notification to assistant with email {}", email);

        String title = payload.get("title");
        String body = payload.get("body");

        if (title == null || title.isEmpty() || body == null || body.isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(Map.of("error", "Invalid request", "message", "Title and body are required"));
        }

        try {
            boolean success = notificationService.sendNotificationByEmail(email, title, body);
            log.debug("End resource send notification to assistant with email {}", email);

            if (success) {
                return ResponseEntity.ok()
                        .body(Map.of("message", "Notification sent successfully"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "Notification failed", "message", "Failed to send notification"));
            }
        } catch (EntityNotFoundException ex) {
            log.error("Assistant not found with email: {}", email, ex);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "Assistant not found", "message", "Assistant not found with email: " + email));
        } catch (TechnicalException ex) {
            log.error("Error sending notification to assistant with email {}: {}", email, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Technical error", "message", ex.getMessage()));
        } catch (Exception ex) {
            log.error("Unexpected error while sending notification to assistant with email {}: {}", email, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error", "message", "An unexpected error occurred"));
        }
    }

}
