package ma.almobadara.backend.controller.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.mobile.BeneficiaryMobileDTO;
import ma.almobadara.backend.dto.mobile.BeneficiaryMobileDetailDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.mobile.BeneficiaryMobileService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/assistant/beneficiary")
@CrossOrigin(origins = "*")
public class BeneficiaryMobileController {

    private final BeneficiaryMobileService beneficiaryMobileService;

    /**
     * Get all beneficiaries for mobile with pagination
     * @param page Optional page number (defaults to 0)
     * @return Page of BeneficiaryMobileDTO
     */
    @GetMapping("/all")
    public ResponseEntity<Page<BeneficiaryMobileDTO>> getAllBeneficiaries(
            @RequestParam(required = false) Optional<Integer> page) {

        logUserInfo("getAllBeneficiariesForMobile", "page: " + page.orElse(0));

        try {
            Page<BeneficiaryMobileDTO> beneficiaries = beneficiaryMobileService.getAllBeneficiariesForMobile(page);
            log.info("End resource getAllBeneficiariesForMobile. Retrieved beneficiaries: {}, OK", beneficiaries.getTotalElements());
            return new ResponseEntity<>(beneficiaries, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource getAllBeneficiariesForMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get a beneficiary by ID
     * @param id The ID of the beneficiary
     * @return BeneficiaryDTO with full details
     */
    @GetMapping("/{id}")
    public ResponseEntity<BeneficiaryDTO> getBeneficiaryById(@PathVariable Long id) {

        logUserInfo("getBeneficiaryByIdMobile", "id: " + id);

        try {
            BeneficiaryDTO beneficiary = beneficiaryMobileService.getBeneficiaryById(id);
            log.info("End resource getBeneficiaryByIdMobile. Retrieved beneficiary ID: {}, OK", id);
            return new ResponseEntity<>(beneficiary, new HttpHeaders(), HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("End resource getBeneficiaryByIdMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (TechnicalException e) {
            log.error("End resource getBeneficiaryByIdMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get beneficiaries for mobile filtered by assistant ID with pagination
     * @param assistantId The ID of the assistant
     * @param page Optional page number (defaults to 0)
     * @return Page of BeneficiaryMobileDTO
     */
    @GetMapping("/by-assistant/{assistantId}")
    public ResponseEntity<Page<BeneficiaryMobileDTO>> getBeneficiariesByAssistantId(
            @PathVariable Long assistantId,
            @RequestParam(required = false) Optional<Integer> page) {

        logUserInfo("getBeneficiariesByAssistantId", "assistantId: " + assistantId + ", page: " + page.orElse(0));

        try {
            Page<BeneficiaryMobileDTO> beneficiaries = beneficiaryMobileService.getBeneficiariesByAssistantId(assistantId, page);
            log.info("End resource getBeneficiariesByAssistantId. Retrieved beneficiaries: {}, OK", beneficiaries.getTotalElements());
            return new ResponseEntity<>(beneficiaries, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("End resource getBeneficiariesByAssistantId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("End resource getBeneficiariesByAssistantId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get detailed beneficiary information for mobile
     * @param id The ID of the beneficiary
     * @return BeneficiaryMobileDetailDTO with comprehensive details
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<BeneficiaryMobileDetailDTO> getBeneficiaryDetailById(@PathVariable Long id) {

        logUserInfo("getBeneficiaryDetailById", "id: " + id);

        try {
            BeneficiaryMobileDetailDTO beneficiary = beneficiaryMobileService.getBeneficiaryDetailById(id);
            log.info("End resource getBeneficiaryDetailById. Retrieved beneficiary ID: {}, OK", id);
            return new ResponseEntity<>(beneficiary, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("End resource getBeneficiaryDetailById. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (TechnicalException e) {
            log.error("End resource getBeneficiaryDetailById. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
