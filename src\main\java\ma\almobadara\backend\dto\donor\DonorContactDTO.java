package ma.almobadara.backend.dto.donor;

import lombok.*;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.DonorContactFunctionDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;

import java.util.Date;
import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonorContactDTO {

	private Long id;

	private String firstName;

	private String lastName;

	private String firstNameAr;

	private String lastNameAr;

	private String sex;

	private String email;

	private String phoneNumber;

	private Date birthDate;

	private Boolean mainContact;

	private DonorMoralDTO donor;

	private DonorContactFunctionDTO donorContactFunction;

	private List<CanalCommunicationDTO> canalCommunications;

	private List<LanguageCommunicationDTO> languageCommunications;

	private List<NoteDonorContactDTO> noteDonorContacts;

}
