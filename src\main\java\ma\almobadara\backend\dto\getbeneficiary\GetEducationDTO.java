package ma.almobadara.backend.dto.getbeneficiary;

import lombok.*;
import ma.almobadara.backend.dto.beneficiary.DocumentEducationDTO;
import ma.almobadara.backend.dto.beneficiary.NoteEducationDTO;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class GetEducationDTO extends RepresentationModel<GetEducationDTO> implements Serializable {

	private static final long serialVersionUID = -1529116668043617328L;

	private Long id;

	private String educationType;

	private String schoolName;

	private String schoolNameAr;

	private Boolean succeed;

	private Double mark;

	private Long cityId;

	private String city;

	private Long schoolYearId;

	private String schoolYear;

	private Long schoolLevelId;

	private String schoolLevel;

	private Long honorId;

	private String honor;

	private Long majorId;

	private String major;

	private Set<NoteEducationDTO> notes;

	private Set<DocumentEducationDTO> documents;

}
