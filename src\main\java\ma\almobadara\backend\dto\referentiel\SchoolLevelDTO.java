package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class SchoolLevelDTO extends RepresentationModel<SchoolLevelDTO> implements Serializable {

	private static final long serialVersionUID = -4039311589723017385L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

	private String type;

}
