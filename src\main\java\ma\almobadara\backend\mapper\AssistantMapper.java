package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.model.administration.Assistant;
import org.mapstruct.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", uses = {CacheAdUserMapper.class})
public interface AssistantMapper {

    @Mappings({
            @Mapping(source = "zone.id", target = "zoneId"),
            @Mapping(source = "zone", target = "zone"),
            @Mapping(source = "cacheAdUser", target = "cacheAdUser"),
            @Mapping(source = "cacheAdUser.id", target = "cacheAdUserId"),
            @Mapping(source = "cacheAdUser.firstName", target = "firstName"),
            @Mapping(source = "cacheAdUser.lastName", target = "lastName"),
            @Mapping(source = "cacheAdUser.mail", target = "email"),
            @Mapping(source = "languageCommunicationIds", target = "languageCommunicationIds", qualifiedByName = "stringToLongList"),
            @Mapping(source = "schoolLevelId", target = "schoolLevel.id"),
            // to know if the assistant has beneficiaries we should  check into the zone into the list of benefirciies if it is empty or not by a expression
            @Mapping(target = "hasBeneficiaries", expression = "java(assistant.getZone() != null && !assistant.getZone().getBeneficiaries().isEmpty())")
    })
    AssistantDTO toDTO(Assistant assistant);

    //updateAssistantFromDTO  :Update the fields of the existing Assistant entity with the new data from the DTO
    // New method to update an existing Assistant entity with the non-null values from AssistantDTO
    @Mappings({
            //  @Mapping(source = "email", target = "user.mail"),
            // @Mapping(target = "user", ignore = true)
            @Mapping(source = "languageCommunicationIds", target = "languageCommunicationIds", qualifiedByName = "longListToString"),
            @Mapping(source = "schoolLevel.id", target = "schoolLevelId"),
            // we should ignore the code because it is not allowed to update it
            @Mapping(target = "code", ignore = true),
            @Mapping(target = "zone", ignore = true),
            @Mapping(target = "cacheAdUser", ignore = true),
            @Mapping(target = "pictureUrl", ignore = true) // Ignore picture URL; handle it in the service


    })
    void updateAssistantFromDTO(AssistantDTO assistantDTO, @MappingTarget Assistant entity);

    @Mappings({
             @Mapping(source = "languageCommunicationIds", target = "languageCommunicationIds", qualifiedByName = "longListToString"),
            @Mapping(source = "schoolLevel.id", target = "schoolLevelId"),
            @Mapping(source = "cacheAdUserId", target = "cacheAdUser.id")
    })
    Assistant toEntity(AssistantDTO assistantDTO);

    @Named("stringToLongList")
    default List<Long> stringToLongList(String languageCommunicationIds) {
        if (languageCommunicationIds == null || languageCommunicationIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(languageCommunicationIds.split(",\\s*"))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    @Named("longListToString")
    default String longListToString(List<Long> languageCommunicationIds) {
        if (languageCommunicationIds == null || languageCommunicationIds.isEmpty()) {
            return "";
        }
        return languageCommunicationIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(", "));
    }
}
