package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CacheAdUser {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cache_ad_user_id_seq")
    @SequenceGenerator(name = "cache_ad_user_id_seq", sequenceName = "cache_ad_user_id_seq", allocationSize = 1)
    private Long id;

    private String azureDirectoryId;

    private String firstName;

    private String lastName;

    private String mail;

    private String mobilePhone;

    private String jobTitle;

    private boolean isDeleted;

    @ManyToOne
    @JoinColumn(name = "role_id")  // Foreign key to Role
    private Role role;

    @ManyToOne
    @JoinColumn(name = "profile_id")
    private Profile profile;

    @CreationTimestamp
    @Column(updatable = false)
    private LocalDateTime creationDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    @UpdateTimestamp
    private LocalDateTime lastLoginInDate;

}
