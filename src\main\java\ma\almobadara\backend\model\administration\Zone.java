package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class Zone {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true)
    private String code;
    private String name;
    @Column(name = "name_ar")
    private String nameAr;
    private String details;
    @Column(name = "is_deleted")
    private boolean isDeleted;
    @Column(name = "city_ids", length = 1000)  // Updated column name
    private String cityIds;  // Store city IDs as a comma-separated string
    @OneToMany(mappedBy = "zone")
    private List<Beneficiary> beneficiaries;
    @CreationTimestamp
    private LocalDateTime createdAt;

    @OneToOne(mappedBy = "zone")
    private Assistant assistant;

    @OneToOne(mappedBy = "zone")
    private AssistantZone assistantZone;

    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "status", nullable = false)
    private boolean status = true;

    @OneToMany(mappedBy = "zone" , cascade = CascadeType.ALL)
    private List<SousZone> sousZones;

    @OneToOne
    @JoinColumn(name = "eps_id", unique = true, nullable = true)
    private Eps eps;
    private Boolean oldZone;

    public List<Long> getCityIdList() {
        if (cityIds == null || cityIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(cityIds.split(",\\s*"))
                .stream()
                .map(Long::parseLong)  // Convert String to Long
                .toList();
    }

    // Method to set city IDs from a list
    public void setCityIdList(List<Long> cityIds) {
        if (cityIds == null || cityIds.isEmpty()) {
            this.cityIds = "";
        } else {
            this.cityIds = String.join(", ", cityIds.stream()
                    .map(String::valueOf)
                    .toList());
        }
    }
}

