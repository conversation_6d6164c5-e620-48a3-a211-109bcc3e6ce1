package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Assistant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.swing.text.html.Option;
import java.util.Optional;

@Repository
public interface AssistantRepository extends JpaRepository<Assistant, Long> {
    // Add any custom query methods if needed

    // find all by page
    // it should be sorted by update date
    @Query("SELECT a FROM Assistant a WHERE a.isDeleted = false ORDER BY a.updateDate DESC")
    Page<Assistant> findAllOrderByUpdateDate(Pageable pageable);

    //existsByZoneId
    boolean existsByZoneId(Long zoneId);

    //findBycode

 Boolean existsByCode(String code);

 Boolean existsAllByEmail(String email);

    //existsByZoneIdAndIdNot
    boolean existsByZoneIdAndIdNot(Long zoneId, Long id);


    Page<Assistant> findByZoneId(Long zoneId, Pageable pageable);

    @Query("SELECT a FROM Assistant a WHERE a.code = (SELECT MAX(a.code) FROM Assistant a)")
    Optional<Assistant> findLastAssistantCode();

    //existsByEmailAndIdNot
    boolean existsByEmailAndIdNot(String email, Long id);

    //findBy zone Id
    Assistant findByZoneId(Long zoneId);

    //findByCacheAdUserId
    Assistant findByCacheAdUserId(Long cacheAdUserId);

    //findByEmailAndPassword
    Optional<Assistant> findByEmailAndPassword(String email, String password);

    //findByEmail
    Optional<Assistant> findByEmail(String email);
}
