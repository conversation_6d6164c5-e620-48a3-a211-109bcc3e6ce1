package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.RolePrivilege;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RolePrivilegeRepository extends JpaRepository<RolePrivilege, Long> {
    List<RolePrivilege> findByRole_Id(Long roleId);
    List<RolePrivilege> findByPrivilege_Id(Long privilegeId);
    List<RolePrivilege> findByFeature_Id(Long featureId);

    //existsByRoleIdAndFeatureCodeAndPrivilegeCode

    boolean existsByRoleIdAndFeatureCodeAndPrivilegeCode(Long roleId, String featureCode, String privilegeCode);
}
