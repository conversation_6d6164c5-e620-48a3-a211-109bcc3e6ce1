package ma.almobadara.backend.service.administration;

import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.dto.beneficiary.SmallZoneDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.SchoolLevelDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserAlreadyExistsException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.mapper.AssistantMapper;
import ma.almobadara.backend.mapper.ZoneMapper;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.administration.HistoryZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.repository.administration.*;
import ma.almobadara.backend.service.donor.MinioService;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AssistantService {
    private final CacheAdUserRepository cacheAdUserRepository;
    private final ZoneRepository zoneRepository;

    private final AssistantRepository assistantRepository;
    private final HistoryZoneRepository historyZoneRepository;
    private final AssistantMapper assistantMapper;
    private final ZoneMapper zoneMapper;
    private final CacheAdUserService cacheAdUserService;
    private final AssistantZoneRepository assistantZoneRepository;
    private final ZoneService zoneService;
    private final RefController refController;
    private final MinioService minioService;

    @Value("${minio.assistantFolder}")
    private String assistantFolder;

    @Value("${minio.profilePicture.folder}")
    private String folderPathPicture;

    @Transactional
    public void createAssistant(AssistantDTO assistantDTO) throws TechnicalException {
        log.debug("Start service create assistant");
        Long userId = null;
        try {
            userId = checkAndSaveUser(assistantDTO.getEmail());
            if (userId != null) {
                Long finalUserId = userId;
                CacheAdUser cacheAdUser = cacheAdUserRepository.findById(userId)
                        .orElseThrow(() -> new EntityNotFoundException("User not found with ID " + finalUserId));
                assistantDTO.setFirstName(cacheAdUser.getFirstName());
                assistantDTO.setLastName(cacheAdUser.getLastName());
            }
            assistantDTO.setCacheAdUserId(userId);


            Assistant assistant = assistantMapper.toEntity(assistantDTO);
            assistant.setCode(generateAssistantCode());

            // Set password if provided
            if (assistantDTO.getPassword() != null && !assistantDTO.getPassword().isEmpty()) {
                assistant.setPassword(assistantDTO.getPassword());
            }

            if (assistantDTO.getPicture() != null) {
                try {
                    String pictureUrl = handlePicture(assistantDTO.getPicture(), assistant);
                    assistant.setPictureUrl(pictureUrl);
                } catch (IOException e) {
                    log.error("Error occurred while saving the assistant: {}", e.getMessage());
                    throw new RuntimeException(e.getMessage(), e);
                }
            }

           assistant.setZone(null);

            Assistant savedAssistant = assistantRepository.save(assistant);
            log.info("Assistant saved with ID {}", savedAssistant.getId());
        } catch (Exception e) {
            if (userId != null) {
                cacheAdUserService.deleteUserById(Math.toIntExact(userId));
            }
            log.error("Error occurred while saving the assistant: {}", e.getMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

        log.debug("End service create assistant");
    }

    @Transactional
    public void updateAssistant(Long id, AssistantDTO assistantDTO) throws TechnicalException {
        log.debug("Start service update assistant with id {}", id);

        // Step 1: Retrieve the existing Assistant entity by ID
        Assistant existingAssistant = assistantRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID " + id));

        // Step 2: Check if the new email is already assigned to another assistant
        if (assistantDTO.getEmail() != null &&
                assistantRepository.existsByEmailAndIdNot(assistantDTO.getEmail(), id)) {
            throw new TechnicalException("Assistant with email " + assistantDTO.getEmail() + " already exists");
        }

        // Step 3: Check if the new zone ID is already assigned to another assistant


        // Step 4: Update other fields and set the update date
        assistantMapper.updateAssistantFromDTO(assistantDTO, existingAssistant);
        existingAssistant.setUpdateDate(LocalDateTime.now());

        // Update password if provided
        if (assistantDTO.getPassword() != null && !assistantDTO.getPassword().isEmpty()) {
            existingAssistant.setPassword(assistantDTO.getPassword());
        }

        if (assistantDTO.getPicture() != null) {
            try {
                String pictureUrl = handlePicture(assistantDTO.getPicture(), existingAssistant);
                existingAssistant.setPictureUrl(pictureUrl);
            } catch (IOException e) {
                log.error("Error occurred while updating the assistant: {}", e.getMessage());
                throw new RuntimeException("Error updating assistant: " + e.getMessage(), e);
            }
        }


        // Step 5: Save the updated assistant
        try {
            assistantRepository.save(existingAssistant);
        } catch (Exception e) {
            log.error("Error occurred while updating the assistant: {}", e.getMessage());
            throw new RuntimeException("Error updating assistant: " + e.getMessage(), e);
        }

        log.debug("End service update assistant with id {}", id);
    }

    private String handlePicture(MultipartFile picture, Assistant assistant) throws IOException {
        Instant instant = Instant.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;

        String assistantPath = assistantFolder
                + assistant.getLastName().toUpperCase() + "-"
                + assistant.getFirstName().substring(0, 1).toUpperCase()
                + assistant.getFirstName().substring(1) + "_"
                + assistant.getCode();

        String fileName = assistant.getLastName().toUpperCase() + "-"
                + assistant.getFirstName().substring(0, 1).toUpperCase()
                + assistant.getFirstName().substring(1) + "_"
                + instant.atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) + "."
                + FilenameUtils.getExtension(picture.getOriginalFilename());

        String pictureUrl = assistantPath + "/" + folderPathPicture + "/" + fileName;

        minioService.WriteToMinIO(
                picture,
                assistantPath + "/" + folderPathPicture + "/",
                fileName
        );

        return pictureUrl;
    }


    public Page<AssistantDTO> getAllAssistants(int page, int size) {
        log.debug("Start service  get all assistants with page {} and size {}", page, size);
        // we should sort the assistants by their update date
        Pageable pageable = PageRequest.of(page, size);
        Page<Assistant> assistants = assistantRepository.findAllOrderByUpdateDate(pageable);
        log.debug("End service  get all assistants with page {} and size {}", page, size);
        return assistants.map(assistantMapper::toDTO);
    }

    public List<AssistantDTO> getAssistants() {
         // we should sort the assistants by their update date

        List<Assistant> assistants = assistantRepository.findAll();
         return assistants.stream().map(assistantMapper::toDTO).toList();
    }

    public AssistantDTO getAssistantById(Long id) throws TechnicalException {

        Assistant assistant = assistantRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + id));

        log.info("Found Assistant: {}", assistant);

        AssistantDTO assistantDTO = assistantMapper.toDTO(assistant);

        List<SmallZoneDTO> smallZones = assistant.getAssistantZones().stream()
                .map(assistantZone -> {
                    Zone zone = assistantZone.getZone();
                    return SmallZoneDTO.builder()
                            .id(zone.getId())
                            .name(zone.getName())
                            .nameAr(zone.getNameAr())
                            .code(zone.getCode())
                            .build();
                })
                .collect(Collectors.toList());

        assistantDTO.setZones(smallZones);

        // Handling SchoolLevelDTO
        if (assistant.getSchoolLevelId() != null) {
            SchoolLevelDTO fullSchoolLevelDTO = refController.getParSchoolLevel(assistant.getSchoolLevelId()).getBody();
            assistantDTO.setSchoolLevel(fullSchoolLevelDTO);
        }
        if(assistant.getPictureUrl() !=null)
        {
            byte[] imageData = minioService.ReadFromMinIO(assistant.getPictureUrl(),null);
            String base64Image = Base64.getEncoder().encodeToString(imageData);
            assistantDTO.setPicture64(base64Image);

        }

        processLanguageCommunicationDetails(assistantDTO);

        return assistantDTO;
    }

    private void processLanguageCommunicationDetails(AssistantDTO assistantDTO) {
        List<Long> languageCommunicationIds = assistantDTO.getLanguageCommunicationIds();
        if (languageCommunicationIds != null && !languageCommunicationIds.isEmpty()) {
            List<LanguageCommunicationDTO> languageCommunicationDetails = languageCommunicationIds.stream()
                    .map(this::fetchLanguageCommunicationDetails)
                    .collect(Collectors.toList());
            assistantDTO.setLanguageCommunicationDetails(languageCommunicationDetails);
        }
    }

    private LanguageCommunicationDTO fetchLanguageCommunicationDetails(Long languageCommunicationIds) {
        return refController.getParLanguageCommunication(languageCommunicationIds).getBody();
    }

    public String generateAssistantCode() {
        // Get the last assistant code
        Optional<Assistant> lastAssistant = assistantRepository.findLastAssistantCode();
        String lastCode = lastAssistant.map(Assistant::getCode).orElse(null);
        String currentYear = String.valueOf(LocalDateTime.now().getYear());
        String newCode;
        if (lastCode != null && lastCode.substring(1, 5).equals(currentYear)) {
            int lastCodeNumber = Integer.parseInt(lastCode.substring(5));
            newCode = "A" + currentYear + String.format("%04d", lastCodeNumber + 1);
        } else {
            newCode = "A" + currentYear + "0001";
        }
        return newCode;
    }


    private Long checkAndSaveUser(String userEmail) throws TechnicalException {
        try {
            return cacheAdUserService.checkAndSaveAdUserByEmail(userEmail);
        } catch (UserAlreadyExistsException | UserNotFoundException e) {
            log.error("Failed to create assistant: {}", e.getMessage());
            throw new TechnicalException(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error occurred while checking and saving user: {}", e.getMessage());
            throw new RuntimeException("An unexpected error occurred while handling user.", e);
        }
    }


    public void deleteAssistant(Long id) throws TechnicalException {
        Assistant assistant = assistantRepository.findById(id).orElse(null);

        if (assistant != null) {
            // we should  check if the assistant has no beneficiaries(mean actif zone thzt related to beneficiaries)
            if (assistantZoneRepository.existsByAssistant(assistant)) {
                throw new TechnicalException("The assistant is already assigned to a zone");
            }
            assistantRepository.deleteById(id);

            if (assistant.getCacheAdUser() != null) {
                try {
                    cacheAdUserService.deleteUserById(Math.toIntExact(assistant.getCacheAdUser().getId()));
                } catch (Exception e) {
                    log.error("Error deleting user with ID {}: {}", assistant.getCacheAdUser().getId(), e.getMessage());
                }
            }

        }

    }




    // get the disponible zones for the assistant = the zones that are not assigned to any assistant
    public List<ZoneDTO> getDisponibleZones() {
        return zoneService.getDisponibleZones();
    }




    public AssistantDTO getAssistantByEmail(String email) throws TechnicalException {
        log.debug("Start service get assistant by email {}", email);

        Assistant assistant = assistantRepository.findByEmail(email)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with email: " + email));

        AssistantDTO assistantDTO = assistantMapper.toDTO(assistant);

        // Handling SchoolLevelDTO
        if (assistant.getSchoolLevelId() != null) {
            SchoolLevelDTO fullSchoolLevelDTO = refController.getParSchoolLevel(assistant.getSchoolLevelId()).getBody();
            assistantDTO.setSchoolLevel(fullSchoolLevelDTO);
        }

        if(assistant.getPictureUrl() != null) {
            try {
                byte[] imageData = minioService.ReadFromMinIO(assistant.getPictureUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                assistantDTO.setPicture64(base64Image);
            } catch (Exception e) {
                log.error("Error reading picture for assistant with email {}: {}", email, e.getMessage());
            }
        }

        processLanguageCommunicationDetails(assistantDTO);

        log.debug("End service get assistant by email {}", email);
        return assistantDTO;
    }

    /**
     * Updates the device token for an assistant
     * @param assistantId The ID of the assistant
     * @param deviceToken The new device token
     * @return The updated AssistantDTO
     * @throws TechnicalException If the assistant is not found or other technical issues occur
     */
    @Transactional
    public AssistantDTO updateDeviceToken(Long assistantId, String deviceToken) throws TechnicalException {
        log.debug("Start service update device token for assistant with id {}", assistantId);

        Assistant assistant = assistantRepository.findById(assistantId)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + assistantId));

        assistant.setDevice_token(deviceToken);
        assistantRepository.save(assistant);

        AssistantDTO assistantDTO = assistantMapper.toDTO(assistant);

        log.debug("End service update device token for assistant with id {}", assistantId);
        return assistantDTO;
    }


//    @Transactional
//    public void changeZone(Long assistantId, LocalDate endDate, Long newZoneId, LocalDate newZoneDate) throws TechnicalException {
//        log.debug("Start service change zone for assistant with id {}", assistantId);
//
//        // Step 1: Retrieve the existing Assistant entity by ID
//        Assistant assistant = assistantRepository.findById(assistantId)
//                .orElseThrow(() -> new TechnicalException("Assistant not found with ID " + assistantId));
//
//        // Step 2: Validate and set the end date
//        if (endDate != null) {
//            assistant.setDateEndAffectationToZone(endDate);
//        }
//
//        // Step 3: Record the current zone in HistoriqueZone before changing
//        if (assistant.getZone() != null) {
//            HistoryZone historique = new HistoryZone();
//            historique.setAssistant(assistant);
//            historique.setZone(assistant.getZone());
//            historique.setDateAffectation(assistant.getDateAffectationToZone());
//            historique.setDateFinAffectation(endDate);
//            historyZoneRepository.save(historique);  // Save the history
//        }
//
//        // Step 4: Change the zone if a new zone ID is provided
//        if (newZoneId != null) {
//            Zone newZone = zoneService.findZoneById(newZoneId);
//            if (newZone == null) {
//                throw new TechnicalException("New zone not found with ID " + newZoneId);
//            }
//            // Check if the zone is already assigned to another assistant
//            if (assistantRepository.existsByZoneIdAndIdNot(newZoneId, assistantId)) {
//                throw new TechnicalException("The zone is already assigned to another assistant");
//            }
//            assistant.setZone(newZone);
//            assistant.setDateAffectationToZone(newZoneDate);
//            assistant.setDateEndAffectationToZone(endDate);
//        }
//
//        // Save the updated Assistant entity
//        assistantRepository.save(assistant);
//
//        log.debug("End service change zone for assistant with id {}", assistantId);
//    }

}
