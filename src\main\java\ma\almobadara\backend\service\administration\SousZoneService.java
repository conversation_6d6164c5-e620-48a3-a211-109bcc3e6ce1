package ma.almobadara.backend.service.administration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.SousZoneDTO;
import ma.almobadara.backend.dto.administration.ZoneWithSousZoneDto;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.SousZoneMapper;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.administration.SousZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.administration.SousZoneRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class SousZoneService {
    private final BeneficiaryRepository beneficiaryRepository;
    private final SousZoneRepository sousZoneRepository;
    private final ZoneRepository zoneRepository;
    private final SousZoneMapper sousZoneMapper;
    private final AssistantRepository assistantRepository;


    public SousZoneDTO addOrUpdateSousZone(Long zoneId, SousZoneDTO sousZoneDTO) {
        // Fetch the parent Zone by ID
        Zone zone = zoneRepository.findById(zoneId)
                .orElseThrow(() -> new ResourceNotFoundException("Zone not found with ID: " + zoneId));

        SousZone sousZone;

        if (sousZoneDTO.getId() != null) {
            // Update existing SousZone
            sousZone = sousZoneRepository.findById(sousZoneDTO.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("SousZone not found with ID: " + sousZoneDTO.getId()));

            // Update fields from DTO
            sousZone.setName(sousZoneDTO.getName());
            sousZone.setNameAr(sousZoneDTO.getNameAr());
            sousZone.setCode(sousZone.getCode()); // Keep the existing code for updates
            sousZone.setDetail(sousZoneDTO.getDetail());
            sousZone.setZone(zone); // Update the Zone if necessary
        } else {
            // Create new SousZone
            sousZone = sousZoneMapper.toEntity(sousZoneDTO);
            sousZone.setZone(zone); // Link the SousZone to the Zone

            // Generate the SousZone code if it's a new entry
            String codeZone = zone.getCode(); // Assuming `Zone` has a `getCode` method
            String newCode = generateSousZoneCode(codeZone); // Call method to generate the code
            sousZone.setCode(newCode);
        }

        // Save the SousZone
        SousZone savedSousZone = sousZoneRepository.save(sousZone);

        // Map Entity back to DTO and return
        return sousZoneMapper.toDto(savedSousZone);
    }

    // Method to generate the SousZone code
    private String generateSousZoneCode(String codeZone) {
        // Prefix for SousZone code
        String prefix = "SZ-" + codeZone + "-";

        // Initialize the sequential number
        int sequentialNumber = 1;

        // Loop to ensure unique code generation
        String newCode;
        do {
            String formattedSequentialNumber = String.format("%03d", sequentialNumber);
            newCode = prefix + formattedSequentialNumber;
            sequentialNumber++;
        } while (sousZoneRepository.existsByCode(newCode));

        return newCode;
    }


    // delete SousZone
    public void deleteSousZone(Long sousZoneId) throws TechnicalException {
        // Fetch the SousZone by ID
        SousZone sousZone = sousZoneRepository.findById(sousZoneId)
                .orElseThrow(() -> new ResourceNotFoundException("SousZone not found with ID: " + sousZoneId));
        // we should check if this sous zone is attached to any other Beneficiary
        boolean isAttachedToBeneficiary = beneficiaryRepository.existsBySousZoneId(sousZoneId);
        if (isAttachedToBeneficiary) {
            throw new TechnicalException("This SousZone is attached to Beneficiaries. It cannot be deleted.");
        }
        // Delete the SousZone
        sousZoneRepository.delete(sousZone);
    }

    // get SousZones by Zone ID
    public List<SousZoneDTO> getSousZonesByZoneId(Long zoneId) {
        // Fetch the parent Zone by ID
        Zone zone = zoneRepository.findById(zoneId)
                .orElseThrow(() -> new ResourceNotFoundException("Zone not found with ID: " + zoneId));

        // Fetch all SousZones by Zone
        List<SousZone> sousZones = sousZoneRepository.findByZone(zone);

        // Map Entities to DTOs and return
        return sousZones.stream()
                .map(sousZoneMapper::toDto)
                .collect(Collectors.toList());
    }

    public List<ZoneWithSousZoneDto> getSousZonesByAssistantId(Long assistantId) {
        Assistant assistant =assistantRepository.findById(assistantId).orElseThrow();

        return assistant.getAssistantZones().stream().map(assistantZone -> {
            Zone zone=assistantZone.getZone();
            ZoneWithSousZoneDto zone1=ZoneWithSousZoneDto.builder()
                    .code(zone.getCode()).id(zone.getId()).name(zone.getName()).nameAr(zone.getNameAr()).dateAffectation(String.valueOf(assistantZone.getDateAffectation())).dateFinAffectation(String.valueOf(assistantZone.getDateEndAffectation())).build();
            zone1.setZoneDTOList(getSousZonesByZoneId(zone1.getId()));
            return zone1;
        }).toList();
    }

}
