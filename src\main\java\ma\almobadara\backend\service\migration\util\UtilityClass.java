package ma.almobadara.backend.service.migration.util;

import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.TechnicalException;
import org.apache.poi.ss.usermodel.Row;

import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class UtilityClass {

    public static String getCellValue(Row row, int cellIndex) {
        try {
            if (row.getCell(cellIndex) != null) {
                switch (row.getCell(cellIndex).getCellType()) {
                    case STRING:
                        return row.getCell(cellIndex).getStringCellValue();
                    case NUMERIC:
                        // Check if the cell contains a date (numeric type)
                        if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(row.getCell(cellIndex))) {
                            // Convert the numeric date to a string formatted as dd/MM/yyyy
                            return new SimpleDateFormat("dd/MM/yyyy").format(row.getCell(cellIndex).getDateCellValue());
                        } else {
                            // Use BigDecimal to avoid scientific notation
                            java.math.BigDecimal bd = new java.math.BigDecimal(row.getCell(cellIndex).getNumericCellValue());
                            return bd.stripTrailingZeros().toPlainString();
                        }
                    case BOOLEAN:
                        return String.valueOf(row.getCell(cellIndex).getBooleanCellValue());
                    case FORMULA:
                        return row.getCell(cellIndex).getCellFormula();
                    default:
                        return "";
                }
            }
        } catch (Exception e) {
            log.error("Error getting cell value from row {} at index {}", row.getRowNum(), cellIndex, e);
        }
        return "";
    }

    public static Date convertStringToDate(String dateString) throws TechnicalException {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null; // Return null for empty values
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        dateFormat.setLenient(false); // Ensures strict parsing

        try {
            return dateFormat.parse(dateString);
        } catch (java.text.ParseException e) {
            throw new TechnicalException("Invalid date format: " + dateString);
        }
    }
}
